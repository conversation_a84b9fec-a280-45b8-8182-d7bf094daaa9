{"name": "app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "axios": "^1.9.0", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "vite": "^6.3.5"}}